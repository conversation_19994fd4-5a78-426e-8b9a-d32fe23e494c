﻿using System;
using HandyControl.Controls;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Services.Dialogs;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Mvvm;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.ViewModels.DialogViewModels
{
    public class WaferInfoDisplayViewModel : ViewModel, IDialogAware
    {
        private readonly StopwatchHelper _stopwatchHelper;

        /// <summary>
        /// definity
        /// </summary>
        public string Title { get => _Title; set => SetProperty(ref _Title, value); }
        private string _Title = "PLC各腔体运行状态";

        /// <summary>
        /// PLC各腔体运行状态
        /// </summary>

        public event Action<IDialogResult> RequestClose;

        public WaferInfoDisplayViewModel()
        {
            _stopwatchHelper = new StopwatchHelper();
            _stopwatchHelper.Restart();

            TimeSpan elapsed = _stopwatchHelper.Stop();
            HcGrowlExtensions.Info($"更新成功，代码执行时间：{elapsed.TotalMilliseconds} 毫秒，当前时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}!");
        }

        private DelegateCommand<string> _RunCmd = null!;

        /// <summary>
        /// 命令处理
        /// </summary>
        public DelegateCommand<string> RunCmd =>
            _RunCmd ?? (_RunCmd = new DelegateCommand<string>(ExecuteRunCmd));

        private void ExecuteRunCmd(string parameter)
        {
            switch (parameter.Trim())
            {
                case "Update":
                    _stopwatchHelper.Restart();
                    TimeSpan elapsed = _stopwatchHelper.Stop();
                    HcGrowlExtensions.Info($"更新成功，代码执行时间：{elapsed.TotalMilliseconds} 毫秒，当前时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}!");

                    break;

                default:
                    HcGrowlExtensions.Info($"未知命令：{parameter}");
                    break;
            }
        }

        public bool CanCloseDialog()
        {
            // 在这里添加逻辑以确定对话框是否可以关闭
            // 例如，您可以检查是否所有必填字段都已填写
            // 如果可以关闭对话框，则返回true；否则返回false
            return true;
        }

        public void OnDialogClosed()
        {
            // 在这里添加当对话框关闭时需要执行的逻辑
            // 例如，您可以清理资源、取消订阅事件等
        }

        public void OnDialogOpened(IDialogParameters parameters)
        {
            // 在这里添加当对话框打开时需要执行的逻辑
            // 例如，您可以根据传入的参数初始化视图模型的状态
            // 使用parameters.GetValue<T>("key")来获取传递的参数值
        }
    }
}