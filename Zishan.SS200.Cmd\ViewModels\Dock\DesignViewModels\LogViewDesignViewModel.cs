﻿using System;
using System.Collections.ObjectModel;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.ViewModels.Dock;

namespace Zishan.SS200.Cmd.ViewModels.Dock.DesignViewModels
{
    /// <summary>
    /// 设计时视图模型，用于XAML设计器中显示模拟数据
    /// </summary>
    public class LogViewDesignViewModel : LogViewModel
    {
        private static LogViewDesignViewModel _Instance;
        public static LogViewDesignViewModel Instance => _Instance ??= new();

        public LogViewDesignViewModel() : base()
        {
            // 设计时数据，覆盖基类的设置
            //this.Title = "设计时 - Wafer 搬运";
        }
    }
}