using System;
using System.Collections.Generic;
using System.IO;
using log4net;
using Newtonsoft.Json;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Models.SS200.SubsystemConfigure;

namespace Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Robot
{
    /// <summary>
    /// 机器人位置参数根配置
    /// </summary>
    public class RobotPositionParametersConfig
    {
        public List<ConfigureSetting> PositionParameters { get; set; }
    }

    /// <summary>
    /// 机器人位置参数提供者 - 从JSON配置文件加载参数
    /// </summary>
    public class RobotPositionParametersProvider : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(RobotPositionParametersProvider));
        private readonly Dictionary<string, int> _parameters = new Dictionary<string, int>();
        private readonly FileSystemWatcher _configWatcher;

        private static readonly Lazy<RobotPositionParametersProvider> _instance =
            new Lazy<RobotPositionParametersProvider>(() => new RobotPositionParametersProvider());

        // 配置文件路径
        private const string CONFIG_PATH = "Configs/SS200/SubsystemConfigure/Robot/RobotPositionParameters.json";

        // 最后修改时间，用于监测配置文件变化
        private DateTime _lastModifiedTime = DateTime.MinValue;

        public static RobotPositionParametersProvider Instance => _instance.Value;

        // 私有构造函数
        private RobotPositionParametersProvider()
        {
            // 初始化文件系统监视器
            string configDir = Path.GetDirectoryName(GetConfigFilePath());
            string configFileName = Path.GetFileName(CONFIG_PATH);

            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            _configWatcher = new FileSystemWatcher(configDir, configFileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            // 注册文件变化事件处理
            _configWatcher.Changed += OnConfigFileChanged;
            _configWatcher.Created += OnConfigFileChanged;

            // 尝试加载配置文件
            LoadFromJson();
        }

        private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 由于文件系统事件可能会触发多次，这里添加简单的防抖动处理
                if ((DateTime.Now - _lastModifiedTime).TotalMilliseconds < 100)
                {
                    return;
                }

                _logger.Info($"检测到配置文件变化: {e.FullPath}, 变化类型: {e.ChangeType}");
                LoadFromJson();
            }
            catch (Exception ex)
            {
                _logger.Error($"处理配置文件变化事件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化默认值（与原常量类相同）
        /// </summary>
        private void InitializeDefaultValues()
        {
            // T轴位置参数
            _parameters["RP1"] = 2500;   // T轴Smooth端到工艺腔室A
            _parameters["RP2"] = 5000;   // T轴Smooth端到工艺腔室B
            _parameters["RP3"] = 7500;   // T轴Smooth端到冷却腔
            _parameters["RP4"] = 10000;  // T轴Smooth端到晶圆盒
            _parameters["RP5"] = 12500;  // T轴Nose端到工艺腔室A
            _parameters["RP6"] = 15000;  // T轴Nose端到工艺腔室B
            _parameters["RP7"] = 17500;  // T轴Nose端到冷却腔
            _parameters["RP8"] = 20000;  // T轴Nose端到晶圆盒
            _parameters["RP9"] = 0;      // T轴零位

            // R轴位置参数
            _parameters["RP10"] = 3000;  // R轴Smooth端伸展并面向工艺腔室A
            _parameters["RP11"] = 3200;  // R轴Smooth端伸展并面向工艺腔室B
            _parameters["RP12"] = 3400;  // R轴Nose端伸展并面向工艺腔室A
            _parameters["RP13"] = 3600;  // R轴Nose端伸展并面向工艺腔室B
            _parameters["RP14"] = 3800;  // R轴Smooth端面向冷却腔并伸展
            _parameters["RP15"] = 4000;  // R轴Nose端伸展并面向冷却腔
            _parameters["RP16"] = 4200;  // R轴Smooth端面向晶圆盒并伸展
            _parameters["RP17"] = 4400;  // R轴Nose端面向晶圆盒并伸展
            _parameters["RP18"] = 0;     // R轴零位

            // Z轴位置参数
            _parameters["RP19"] = 1500;  // Z轴在Smooth端到工艺腔室A的高度
            _parameters["RP20"] = 1600;  // Z轴在Smooth端到工艺腔室B的高度
            _parameters["RP21"] = 1700;  // Z轴在Smooth端到冷却腔的高度
            _parameters["RP22"] = 1800;  // Z轴在Smooth端到冷却底部的高度
            _parameters["RP23"] = Golbal.BasePinSearchResultVallue;  // Z轴在Nose端到工艺腔室A的高度
            _parameters["RP24"] = 2000;  // Z轴在Nose端到工艺腔室B的高度
            _parameters["RP25"] = 2100;  // Z轴在Nose端到冷却顶部取片高度
            _parameters["RP26"] = 2200;  // Z轴在Nose端到冷却底部取片高度
            _parameters["RP27"] = 0;     // Z轴零位
            _parameters["RP28"] = 1000;  // Z轴到插销检测高度
        }

        /// <summary>
        /// 从JSON配置文件加载参数
        /// </summary>
        /// <returns>是否加载成功</returns>
        public bool LoadFromJson()
        {
            try
            {
                string jsonFilePath = GetConfigFilePath();
                if (!File.Exists(jsonFilePath))
                {
                    _logger.Warn($"机器人位置参数配置文件不存在: {jsonFilePath}，将使用默认值");
                    return false;
                }

                // 获取文件最后修改时间
                DateTime currentModified = File.GetLastWriteTime(jsonFilePath);

                // 如果文件未修改，则不重新加载
                if (currentModified == _lastModifiedTime)
                {
                    return true;
                }

                _lastModifiedTime = currentModified;

                string jsonContent;
                using (var fileStream = new FileStream(jsonFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    jsonContent = reader.ReadToEnd();
                }
                var config = JsonConvert.DeserializeObject<RobotPositionParametersConfig>(jsonContent);

                if (config?.PositionParameters == null || config.PositionParameters.Count == 0)
                {
                    _logger.Warn("未找到有效的位置参数配置，将使用默认值");
                    return false;
                }

                // 临时字典，验证成功后再替换
                var tempParameters = new Dictionary<string, int>();
                foreach (var param in config.PositionParameters)
                {
                    if (string.IsNullOrEmpty(param.Code))
                    {
                        _logger.Warn($"参数ID {param.Id} 缺少代码标识，已跳过");
                        continue;
                    }

                    tempParameters[param.Code] = param.IntValue;
                    _logger.Debug($"加载参数 {param.Code} = {param.Value} ({param.Description})");
                }

                // 验证所有必要参数都存在
                for (int i = 1; i <= 28; i++)
                {
                    string code = $"RP{i}";
                    if (!tempParameters.ContainsKey(code))
                    {
                        _logger.Warn($"配置文件中缺少必要参数 {code}，将使用默认值");
                        tempParameters[code] = _parameters[code]; // 使用默认值
                    }
                }

                // 更新参数字典
                foreach (var kvp in tempParameters)
                {
                    _parameters[kvp.Key] = kvp.Value;
                }

                _logger.Info($"成功从 {jsonFilePath} 加载 {tempParameters.Count} 个机器人位置参数");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载机器人位置参数配置文件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        private string GetConfigFilePath()
        {
            try
            {
                return App.ConfigHelper.GetConfigFilePath(CONFIG_PATH);
            }
            catch (Exception ex)
            {
                _logger.Error($"获取配置文件路径失败: {ex.Message}", ex);

                // 回退策略 - 尝试直接拼接路径
                string fallbackPath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    CONFIG_PATH);

                _logger.Info($"使用回退路径: {fallbackPath}");
                return fallbackPath;
            }
        }

        /// <summary>
        /// 获取参数值
        /// </summary>
        /// <param name="enuRobotPositionParameterCodes">参数代码 (如 "RP1")</param>
        /// <returns>参数值</returns>
        public int GetParameterValue(EnuRobotPositionParameterCodes enuRobotPositionParameterCodes)
        {
            if (_parameters.TryGetValue(enuRobotPositionParameterCodes.ToString(), out int value))
            {
                return value;
            }

            throw new KeyNotFoundException($"找不到机器人位置参数: {enuRobotPositionParameterCodes}");
        }

        /// <summary>
        /// 根据值获取对应的键列表
        /// </summary>
        /// <param name="stepValue"></param>
        /// <returns></returns>
        public List<string> GetKeyByValue(int stepValue)
        {
            List<string> keys = new List<string>();
            // 反向查找值对应的键
            foreach (var kvp in _parameters)
            {
                if (kvp.Value == stepValue)
                {
                    keys.Add(kvp.Key);
                }
            }
            return keys;
        }

        #region 辅助方法 - 与原常量类兼容

        /// <summary>
        /// 根据端口类型和站点类型获取T轴位置参数
        /// </summary>
        public int GetTAxisPosition(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            switch (endType)
            {
                case EnuRobotEndType.Smooth:
                    switch (stationType)
                    {
                        case EnuLocationStationType.ChamberA:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP1);

                        case EnuLocationStationType.ChamberB:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP2);

                        case EnuLocationStationType.CoolingChamber:
                        case EnuLocationStationType.CoolingTop:
                        case EnuLocationStationType.CoolingBottom:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP3);

                        case EnuLocationStationType.Cassette:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP4);

                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                case EnuRobotEndType.Nose:
                    switch (stationType)
                    {
                        case EnuLocationStationType.ChamberA:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP5);

                        case EnuLocationStationType.ChamberB:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP6);

                        case EnuLocationStationType.CoolingChamber:
                        case EnuLocationStationType.CoolingTop:
                        case EnuLocationStationType.CoolingBottom:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP7);

                        case EnuLocationStationType.Cassette:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP8);

                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                default:
                    throw new ArgumentException($"不支持的端口类型: {endType}");
            }
        }

        /// <summary>
        /// 获取T轴零位参数
        /// </summary>
        public int GetTAxisZeroPosition()
        {
            return GetParameterValue(EnuRobotPositionParameterCodes.RP9);
        }

        /// <summary>
        /// 根据端口类型和站点类型获取R轴位置参数
        /// </summary>
        public int GetRAxisPosition(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            switch (endType)
            {
                case EnuRobotEndType.Smooth:
                    switch (stationType)
                    {
                        case EnuLocationStationType.ChamberA:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP10);

                        case EnuLocationStationType.ChamberB:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP11);

                        case EnuLocationStationType.CoolingTop:
                        case EnuLocationStationType.CoolingBottom:
                        case EnuLocationStationType.CoolingChamber:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP14);

                        case EnuLocationStationType.Cassette:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP16);

                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                case EnuRobotEndType.Nose:
                    switch (stationType)
                    {
                        case EnuLocationStationType.ChamberA:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP12);

                        case EnuLocationStationType.ChamberB:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP13);

                        case EnuLocationStationType.CoolingTop:
                        case EnuLocationStationType.CoolingBottom:
                        case EnuLocationStationType.CoolingChamber:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP15);

                        case EnuLocationStationType.Cassette:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP17);

                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                default:
                    throw new ArgumentException($"不支持的端口类型: {endType}");
            }
        }

        /// <summary>
        /// 获取R轴零位参数
        /// </summary>
        public int GetRAxisZeroPosition()
        {
            return GetParameterValue(EnuRobotPositionParameterCodes.RP18);
        }

        /// <summary>
        /// 根据端口类型和站点类型获取Z轴取片位置参数
        /// </summary>
        public int GetZAxisGetPosition(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            switch (endType)
            {
                case EnuRobotEndType.Smooth:
                    switch (stationType)
                    {
                        case EnuLocationStationType.ChamberA:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP19);

                        case EnuLocationStationType.ChamberB:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP20);

                        case EnuLocationStationType.CoolingTop:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP21);

                        case EnuLocationStationType.CoolingBottom:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP22);

                        case EnuLocationStationType.Cassette:
                            // 晶圆盒取片高度需要根据插槽号动态计算
                            throw new NotSupportedException("晶圆盒取片高度需要通过其他方法计算");
                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                case EnuRobotEndType.Nose:
                    switch (stationType)
                    {
                        case EnuLocationStationType.ChamberA:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP23);

                        case EnuLocationStationType.ChamberB:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP24);

                        case EnuLocationStationType.CoolingTop:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP25);

                        case EnuLocationStationType.CoolingBottom:
                            return GetParameterValue(EnuRobotPositionParameterCodes.RP26);

                        case EnuLocationStationType.Cassette:
                            // 晶圆盒取片高度需要根据插槽号动态计算
                            throw new NotSupportedException("晶圆盒取片高度需要通过其他方法计算");
                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                default:
                    throw new ArgumentException($"不支持的端口类型: {endType}");
            }
        }

        /// <summary>
        /// 获取Z轴零位参数
        /// </summary>
        public int GetZAxisZeroPosition()
        {
            return GetParameterValue(EnuRobotPositionParameterCodes.RP27);
        }

        /// <summary>
        /// 获取Z轴做Pin Search需要的初始高度
        /// </summary>
        public int GetZAxisPinSearchPosition()
        {
            return GetParameterValue(EnuRobotPositionParameterCodes.RP28);
        }

        public string GetAxisTypeCode(int axisType)
        {
            switch (axisType)
            {
                case 0:
                    return "T轴";

                case 1:
                    return "R轴";

                case 2:
                    return "Z轴";

                default:
                    throw new ArgumentException($"不支持的轴类型代码: {axisType}");
            }
        }

        #endregion 辅助方法 - 与原常量类兼容

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_configWatcher != null)
            {
                _configWatcher.EnableRaisingEvents = false;
                _configWatcher.Changed -= OnConfigFileChanged;
                _configWatcher.Created -= OnConfigFileChanged;
                _configWatcher.Dispose();
            }
        }
    }
}