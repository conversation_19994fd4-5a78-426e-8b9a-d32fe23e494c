﻿using System.Collections.ObjectModel;
using System.Threading;
using Zishan.SS200.Cmd.DTO;
using Zishan.SS200.Cmd.Models.IR400;
using Zishan.SS200.Cmd.Models.Shared;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 定义静态全局变量
    /// </summary>
    public static class Golbal
    {
        /// <summary>
        /// 左边可创建的Wafer ID列表
        /// </summary>
        public static ObservableCollection<Wafer> CurLeftAvailableWafers { get; set; } = new ObservableCollection<Wafer>();

        /// <summary>
        /// 右边可创建的Wafer ID列表
        /// </summary>
        public static ObservableCollection<Wafer> CurRightAvailableWafers { get; set; } = new ObservableCollection<Wafer>();

        /// <summary>
        /// 共享同步当前运行条码配方信息，用于Top最大Slot号控制
        /// </summary>
        public static DtoRunRecipeInfo CurGolbalRunRecipeInfo { get; set; } = new DtoRunRecipeInfo();

        /// <summary>
        /// 工作根目录路径,默认：C:\SS200Modbus
        /// </summary>
        public static string WorkRootPath = @"C:\SS200Modbus";

        /// <summary>
        /// Cassette Slot最小值，默认：1
        /// </summary>
        public static int CassetteSlotMin { get; set; } = 1;

        /// <summary>
        /// Cassette Slot最大值,默认：25
        /// </summary>
        public static int CassetteSlotMax { get; set; } = 25;

        /// <summary>
        /// 命令标识信息寄存器长度，默认：0x10
        /// </summary>SS
        public static int CmdFlagInfoRegLenth = 0x10;

        /// <summary>
        /// 命令参数寄存器长度，默认：0x20
        /// </summary>
        public static int CmdParameterRegLenth = 0x20;

        /// <summary>
        /// 创建自定义下一个左边Wafer序号
        /// </summary>
        public static int CurLeftWafersId { get; set; } = 25;

        /// <summary>
        /// 创建自定义下一个右边Wafer序号
        /// </summary>
        public static int CurRightWafersId { get; set; } = 25;

        /// <summary>
        /// UI额外Wafer标识信息 Cassette1
        /// </summary>
        public static string TagInf_Cassette = "Cassette1";

        /// <summary>
        /// UI额外Wafer标识信息 Cassette2
        /// </summary>
        public static string TagInf_CassettteTarget = "Cassette2";

        #region 命令交互共享变量

        public static bool _IsRun;

        public static bool _IsUseRedis = true;

        /// <summary>
        /// 参数保存PLC触发地址，写入TRUE，PLC断电重启保存，例如：GVL.SaveParameter
        /// </summary>
        public static string SaveParameter = "GVL.SaveParameter";

        #endregion 命令交互共享变量

        /// <summary>
        /// 是否为开发调试模式
        /// </summary>
        public static bool IsDevDebug = false;

        /// <summary>
        /// Debug命令运行超时时间，默认：1000毫秒
        /// </summary>
        public static int DebugCommandRunTimeout = 1000;

        /// <summary>
        /// 程序运行耗时时间
        /// </summary>
        public static string RunTotalElapsedtime = "0秒";

        /// <summary>
        /// 导入导出文件夹初始化路径常量定义 C:\IR400\Parameter
        /// </summary>
        public static string ImportExportFolderPath = "C:\\IR400\\Parameter";

        /// <summary>
        /// 导入导出程序配置JSON文件名： CurAppConfig.jsonAppConfig
        /// </summary>
        public static string JsonAppConfigFileName = "CurAppConfig.jsonAppConfig";

        /// <summary>
        /// 命令运行超时时间，默认：5000毫秒
        /// </summary>
        public static int CommandRunTimeout = 5000;

        /// <summary>
        /// IP地址
        /// </summary>
        public static string IpAddress = "127.0.0.1";

        /// <summary>
        /// Secs连接状态
        /// </summary>
        public static string SecsConnStates = "Disable";

        /// <summary>
        /// 当前用户信息表
        /// </summary>
        public static Base_User CurUser { get; set; } = new Base_User() { UserName = "Admin", Password = "123456" };//default Unknown

        /// <summary>
        /// 全局程序退出取消任务
        /// </summary>
        public static readonly CancellationTokenSource CtsAppExit = new CancellationTokenSource();

        /// <summary>
        /// 强制设置PinSearch基准值为：1900步
        /// </summary>
        public static int BasePinSearchResultVallue = 1900;
    }
}