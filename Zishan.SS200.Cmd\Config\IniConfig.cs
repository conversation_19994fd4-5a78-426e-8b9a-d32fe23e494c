﻿using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.Config
{
    public class IniConfig
    {
        /// <summary>
        /// Modbus 服务端IP地址
        /// </summary>
        public string Ip { get; set; }

        /// <summary>
        /// Modbus 服务端端口
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 是否自动启动，默认：True
        /// </summary>
        public bool AutoStart { get; set; }

        /// <summary>
        /// Shuttle设备IP地址
        /// </summary>
        public string ShuttleIp { get; set; }

        /// <summary>
        /// Shuttle设备端口
        /// </summary>
        public int ShuttlePort { get; set; }

        /// <summary>
        /// Robot设备IP地址
        /// </summary>
        public string RobotIp { get; set; }

        /// <summary>
        /// Robot设备端口
        /// </summary>
        public int RobotPort { get; set; }

        /// <summary>
        /// Cha设备IP地址
        /// </summary>
        public string ChaIp { get; set; }

        /// <summary>
        /// Cha设备端口
        /// </summary>
        public int ChaPort { get; set; }

        /// <summary>
        /// Chb设备IP地址
        /// </summary>
        public string ChbIp { get; set; }

        /// <summary>
        /// Chb设备端口
        /// </summary>
        public int ChbPort { get; set; }

        /// <summary>
        /// 是否在UI日志中显示调用方法名和行号，默认：false
        /// </summary>
        public bool ShowCallerInfoInUILog { get; set; } = false;

        /// <summary>
        /// Z轴安全最低点，小于该值不允许：
        /// </summary>
        public int ZAxixMinSafeValue { get; set; }

        /// <summary>
        /// 数据库访问类型: 0：Dev本地数据库开发模式，1：Test测试模式，2：Product_In(无尘室内网络wuchenshi)生产模式，3：Product_Out(无尘室外zishan网络)生产模式
        /// </summary>
        public EnuDatabaseAccessType DatabaseAccessType { get; set; }

        public override string ToString()
        {
            var strMsg = $"Ip: {Ip}, Port: {Port}, AutoStart: {AutoStart}, " +
                        $"Shuttle: {ShuttleIp}:{ShuttlePort}, " +
                        $"Robot: {RobotIp}:{RobotPort}, " +
                        $"ChamberA: {ChaIp}:{ChaPort}, " +
                        $"ChamberB: {ChbIp}:{ChbPort}";
            return strMsg;
        }
    }
}