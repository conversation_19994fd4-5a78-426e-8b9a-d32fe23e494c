using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Runtime.CompilerServices;
using log4net;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 性能计时辅助类，支持自动日志记录和性能分析
    /// </summary>
    public class StopwatchHelper
    {
        private readonly Stopwatch stopwatch = new();
        private readonly string _operationName;
        private readonly string _callerMemberName;
        private readonly int _callerLineNumber;
        private readonly string _callerFilePath;
        private static readonly ILog _perfLogger = LogManager.GetLogger("Performance");
        private static readonly ILog _logger = LogManager.GetLogger(typeof(StopwatchHelper));

        /// <summary>
        /// 创建计时器实例
        /// </summary>
        /// <param name="operationName">操作名称，用于日志标识</param>
        /// <param name="callerMemberName">调用方法名（自动获取）</param>
        /// <param name="callerLineNumber">调用行号（自动获取）</param>
        /// <param name="callerFilePath">调用文件路径（自动获取）</param>
        public StopwatchHelper(string operationName = "",
            [CallerMemberName] string callerMemberName = "",
            [CallerLineNumber] int callerLineNumber = 0,
            [CallerFilePath] string callerFilePath = "")
        {
            _operationName = string.IsNullOrEmpty(operationName) ? callerMemberName : operationName;
            _callerMemberName = callerMemberName;
            _callerLineNumber = callerLineNumber;
            _callerFilePath = System.IO.Path.GetFileName(callerFilePath);
        }

        /// <summary>
        /// 开始计时
        /// </summary>
        /// <param name="logStart">是否记录开始日志</param>
        public void Start(bool logStart = true)
        {
            stopwatch.Start();

            if (logStart)
            {
                var startMessage = $"[性能计时开始] 操作: {_operationName} | 位置: {_callerFilePath}:{_callerMemberName}:{_callerLineNumber}";
                _perfLogger.Info(startMessage);
                _logger.Debug(startMessage);
            }
        }

        /// <summary>
        /// 停止计时并返回耗时
        /// </summary>
        /// <param name="logResult">是否记录结果日志</param>
        /// <param name="warnThresholdMs">警告阈值（毫秒），超过此值将记录警告日志</param>
        /// <returns>计时结果</returns>
        public TimeSpan Stop(bool logResult = true, double warnThresholdMs = 1000)
        {
            if (!stopwatch.IsRunning)
                throw new InvalidOperationException("计时器未启动。");

            stopwatch.Stop();
            var elapsed = stopwatch.Elapsed;

            if (logResult)
            {
                LogPerformanceResult(elapsed, warnThresholdMs);
            }

            return elapsed;
        }

        /// <summary>
        /// 重启计时器并返回上次的耗时
        /// </summary>
        /// <param name="logResult">是否记录上次结果日志</param>
        /// <param name="warnThresholdMs">警告阈值（毫秒）</param>
        /// <returns>上次计时结果</returns>
        public TimeSpan Restart(bool logResult = true, double warnThresholdMs = 1000)
        {
            var elapsed = stopwatch.Elapsed;

            if (logResult && elapsed.TotalMilliseconds > 0)
            {
                LogPerformanceResult(elapsed, warnThresholdMs);
            }

            stopwatch.Restart();

            var restartMessage = $"[性能计时重启] 操作: {_operationName} | 位置: {_callerFilePath}:{_callerMemberName}:{_callerLineNumber}";
            _perfLogger.Info(restartMessage);
            _logger.Debug(restartMessage);

            return elapsed;
        }

        /// <summary>
        /// 重置计时器
        /// </summary>
        public void Reset()
        {
            stopwatch.Reset();

            var resetMessage = $"[性能计时重置] 操作: {_operationName} | 位置: {_callerFilePath}:{_callerMemberName}:{_callerLineNumber}";
            _logger.Debug(resetMessage);
        }

        /// <summary>
        /// 获取当前已计时时长（不停止计时器）
        /// </summary>
        public TimeSpan Elapsed => stopwatch.Elapsed;

        /// <summary>
        /// 计时器是否正在运行
        /// </summary>
        public bool IsRunning => stopwatch.IsRunning;

        /// <summary>
        /// 记录性能结果日志
        /// </summary>
        /// <param name="elapsed">耗时</param>
        /// <param name="warnThresholdMs">警告阈值</param>
        private void LogPerformanceResult(TimeSpan elapsed, double warnThresholdMs)
        {
            var elapsedMs = elapsed.TotalMilliseconds;
            var resultMessage = $"[性能计时结果] 操作: {_operationName} | 耗时: {elapsedMs:F2}ms ({elapsed:hh\\:mm\\:ss\\.fff}) | 位置: {_callerFilePath}:{_callerMemberName}:{_callerLineNumber}";

            // 记录到性能日志
            _perfLogger.Info(resultMessage);

            // 根据耗时决定日志级别
            if (elapsedMs > warnThresholdMs)
            {
                var warnMessage = $"[性能警告] {resultMessage} | 超过阈值: {warnThresholdMs}ms";
                _logger.Warn(warnMessage);
                AppLog.Warn(warnMessage);
            }
            else if (elapsedMs > warnThresholdMs * 0.7) // 70%阈值记录Info
            {
                _logger.Info(resultMessage);
            }
            else
            {
                _logger.Debug(resultMessage);
            }
        }

        /// <summary>
        /// 静态方法：测量代码块执行时间
        /// </summary>
        /// <param name="action">要测量的代码块</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="logResult">是否记录结果</param>
        /// <param name="warnThresholdMs">警告阈值（毫秒）</param>
        /// <param name="callerMemberName">调用方法名（自动获取）</param>
        /// <param name="callerLineNumber">调用行号（自动获取）</param>
        /// <param name="callerFilePath">调用文件路径（自动获取）</param>
        /// <returns>执行耗时</returns>
        public static TimeSpan Measure(Action action,
            string operationName = "",
            bool logResult = true,
            double warnThresholdMs = 1000,
            [CallerMemberName] string callerMemberName = "",
            [CallerLineNumber] int callerLineNumber = 0,
            [CallerFilePath] string callerFilePath = "")
        {
            var helper = new StopwatchHelper(operationName, callerMemberName, callerLineNumber, callerFilePath);
            helper.Start(logResult);

            try
            {
                action();
                return helper.Stop(logResult, warnThresholdMs);
            }
            catch (Exception ex)
            {
                var elapsed = helper.Stop(logResult, warnThresholdMs);
                var errorMessage = $"[性能计时异常] 操作: {operationName} | 耗时: {elapsed.TotalMilliseconds:F2}ms | 异常: {ex.Message}";
                _logger.Error(errorMessage, ex);
                AppLog.Error(errorMessage, ex);
                throw;
            }
        }

        /// <summary>
        /// 静态方法：测量异步代码块执行时间
        /// </summary>
        /// <param name="asyncAction">要测量的异步代码块</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="logResult">是否记录结果</param>
        /// <param name="warnThresholdMs">警告阈值（毫秒）</param>
        /// <param name="callerMemberName">调用方法名（自动获取）</param>
        /// <param name="callerLineNumber">调用行号（自动获取）</param>
        /// <param name="callerFilePath">调用文件路径（自动获取）</param>
        /// <returns>执行耗时</returns>
        public static async Task<TimeSpan> MeasureAsync(Func<Task> asyncAction,
            string operationName = "",
            bool logResult = true,
            double warnThresholdMs = 1000,
            [CallerMemberName] string callerMemberName = "",
            [CallerLineNumber] int callerLineNumber = 0,
            [CallerFilePath] string callerFilePath = "")
        {
            var helper = new StopwatchHelper(operationName, callerMemberName, callerLineNumber, callerFilePath);
            helper.Start(logResult);

            try
            {
                await asyncAction();
                return helper.Stop(logResult, warnThresholdMs);
            }
            catch (Exception ex)
            {
                var elapsed = helper.Stop(logResult, warnThresholdMs);
                var errorMessage = $"[性能计时异常] 操作: {operationName} | 耗时: {elapsed.TotalMilliseconds:F2}ms | 异常: {ex.Message}";
                _logger.Error(errorMessage, ex);
                AppLog.Error(errorMessage, ex);
                throw;
            }
        }
    }
}