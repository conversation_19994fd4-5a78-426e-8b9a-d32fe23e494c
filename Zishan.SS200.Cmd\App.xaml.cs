using System;
using System.Diagnostics;
using Prism.DryIoc;
using Prism.Ioc;
using System.Windows;
using Zishan.SS200.Cmd.Views;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.ViewModels;
using log4net.Config;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using log4net;
using Wu.Wpf.Common;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Config;
using Zishan.SS200.Cmd.Constants;
using Zishan.SS200.Cmd.Models.Shared;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Shuttle;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.ViewModels.DialogViewModels;
using Zishan.SS200.Cmd.ViewModels.Dock;
using Zishan.SS200.Cmd.Views.Dialogs;

namespace Zishan.SS200.Cmd
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : PrismApplication
    {
        /// <summary>
        ///  单例
        /// </summary>
        private static Mutex IMutex;

        private readonly ILog _logger = LogManager.GetLogger(typeof(MainWindowViewModel));

        /// <summary>
        /// 应用程序配置信息
        /// </summary>
        public static IniConfig AppIniConfig = new IniConfig();

        /// <summary>
        /// 从IOC容器中获取指定类型的实例
        /// </summary>
        /// <typeparam name="T">要获取的实例类型</typeparam>
        /// <returns>指定类型的实例</returns>
        /// <exception cref="InvalidOperationException">当应用程序未初始化或容器不可用时抛出</exception>
        /// <example>
        /// // 获取SS200InterLock实例
        /// var interLock = App.GetInstance<SS200InterLock>();
        ///
        /// // 获取IS200McuCmdService实例
        /// var mcuService = App.GetInstance<IS200McuCmdService>();
        ///
        /// // 获取MainWindowViewModel实例
        /// var mainViewModel = App.GetInstance<MainWindowViewModel>();
        /// </example>
        public static T GetInstance<T>()
        {
            try
            {
                // 检查应用程序是否已初始化
                if (Current == null)
                {
                    throw new InvalidOperationException("应用程序未初始化，无法获取IOC容器实例");
                }

                // 获取当前应用程序实例
                if (Current is App app)
                {
                    // 使用容器的Resolve方法获取实例
                    return app.Container.Resolve<T>();
                }
                else
                {
                    throw new InvalidOperationException("当前应用程序实例不是App类型，无法访问IOC容器");
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志
                var logger = LogManager.GetLogger(typeof(App));
                logger.Error($"从IOC容器获取实例失败，类型: {typeof(T).Name}, 错误: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 从IOC容器中获取指定类型的实例（带参数）
        /// </summary>
        /// <typeparam name="T">要获取的实例类型</typeparam>
        /// <param name="parameters">构造函数参数</param>
        /// <returns>指定类型的实例</returns>
        /// <exception cref="InvalidOperationException">当应用程序未初始化或容器不可用时抛出</exception>
        /// <example>
        /// // 带参数获取实例
        /// var instance = App.GetInstance<SomeService>((typeof(string), "paramValue"));
        /// </example>
        public static T GetInstance<T>(params (Type Type, object Instance)[] parameters)
        {
            try
            {
                // 检查应用程序是否已初始化
                if (Current == null)
                {
                    throw new InvalidOperationException("应用程序未初始化，无法获取IOC容器实例");
                }

                // 获取当前应用程序实例
                if (Current is App app)
                {
                    // 使用容器的Resolve方法获取实例，传入参数
                    return app.Container.Resolve<T>(parameters);
                }
                else
                {
                    throw new InvalidOperationException("当前应用程序实例不是App类型，无法访问IOC容器");
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志
                var logger = LogManager.GetLogger(typeof(App));
                logger.Error($"从IOC容器获取实例失败，类型: {typeof(T).Name}, 参数: {string.Join(", ", parameters.Select(p => p.Type.Name))}, 错误: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 配置文件辅助类
        /// </summary>
        public static class ConfigHelper
        {
            private static readonly ILog _logger = LogManager.GetLogger(typeof(ConfigHelper));

            /// <summary>
            /// 获取配置文件路径，优先使用工作目录中的文件
            /// </summary>
            /// <param name="relativePath">相对于Configs目录的路径</param>
            /// <returns>配置文件的完整路径</returns>
            /// <example>
            /// // 获取MotorAlarmInfo.json配置文件路径
            /// string configPath = App.ConfigHelper.GetConfigFilePath("Configs/AlarmInfo/MotorAlarmInfo.json");
            ///
            /// // 或者不带Configs前缀
            /// string configPath = App.ConfigHelper.GetConfigFilePath("AlarmInfo/MotorAlarmInfo.json");
            /// </example>
            public static string GetConfigFilePath(string relativePath)
            {
                // 获取工作目录中的路径
                string workingPath = ConfigPaths.GetWorkingConfigFilePath(relativePath);

                // 如果工作目录中的文件存在，则使用该文件
                if (File.Exists(workingPath))
                {
                    _logger.Debug($"使用工作目录配置文件: {workingPath}");
                    return workingPath;
                }
                else
                {
                    _logger.Error($"工作目录配置文件不存在: {workingPath}");
                    throw new FileNotFoundException($"找不到配置文件: {workingPath}");
                }

                // 否则回退到应用程序目录
                // string appPath = Path.Combine(ConfigPaths.AppConfigDir,
                //     relativePath.StartsWith("Configs/", StringComparison.OrdinalIgnoreCase)
                //         ? relativePath.Substring(8)
                //         : relativePath);
                //
                // _logger.Debug($"使用应用程序目录配置文件: {appPath}");
                // return appPath;
            }
        }

        protected override Window CreateShell()
        {
            return Container.Resolve<MainWindow>();
        }

        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {
            //注册自定义对话主机服务
            containerRegistry.Register<IDialogHostService, DialogHostService>();
            //containerRegistry.RegisterDialog<LoginView, LoginViewModel>();
            //containerRegistry.RegisterDialog<RobotParameterSetting, RobotParameterSettingViewModel>();
            containerRegistry.RegisterDialog<WaferInfoDisplay, WaferInfoDisplayViewModel>();

            // 注册 ModbusClientService 为单例服务
            containerRegistry.RegisterSingleton<IModbusClientService, ModbusClientService>();

            // 注册 ModbusRegisterService
            //containerRegistry.RegisterSingleton<ModbusRegisterService>();

            // 注册 S200McuCmdService 为单例
            containerRegistry.RegisterSingleton<IS200McuCmdService, S200McuCmdService>();
            //containerRegistry.RegisterSingleton<S200McuCmdService>();// 兼容旧代码，后面考虑删除

            // 注册 MainWindowViewModel 为单例
            containerRegistry.RegisterSingleton<MainWindowViewModel>();
            containerRegistry.RegisterSingleton<RobotStatusPanelViewModel>();
            containerRegistry.RegisterSingleton<ModbusDICoilsPanelViewModel>();
            containerRegistry.RegisterSingleton<ModbusDOCoilsPanelViewModel>();
            containerRegistry.RegisterSingleton<LogViewModel>();
            containerRegistry.RegisterSingleton<TransferWaferViewModel>();

            // 注册 IntegerConversionViewModel
            containerRegistry.RegisterSingleton<IntegerConversionViewModel>();
            containerRegistry.RegisterSingleton<RobotSubsystemStatus>();

            // 修复：为ChamberA和ChamberB分别注册独立的单例子类
            containerRegistry.RegisterSingleton<ChamberASubsystemStatus>();
            containerRegistry.RegisterSingleton<ChamberBSubsystemStatus>();
            // 注意：ChamberSubsystemStatus是抽象类，不能注册为可实例化的类型

            containerRegistry.RegisterSingleton<ShuttleSubsystemStatus>();
            containerRegistry.RegisterSingleton<SS200InterLockMain>();//封装统一入口访问SS200设备的互锁状态条件信息和报警信息

            // 注册配置验证器
            containerRegistry.RegisterSingleton<Services.SS200ConfigurationValidator>();

            // 注册 TrasferWaferTestViewModel
            containerRegistry.RegisterForNavigation<TransferWafer, TransferWaferViewModel>();
            containerRegistry.RegisterForNavigation<BasicCommandTest, BasicCommandTestViewModel>();

            containerRegistry.RegisterForNavigation<RunRecipe, RunRecipeViewModel>();

            // 注册视图和视图模型
            // containerRegistry.RegisterForNavigation<IR400View, IR400ViewModel>();

            #region 注册全局共享 AdsHelper、RecipeNames、Operator   配方信息：CH配方、Cooling配方、Sequence

            // 通过反序列化 获取WorkingIR400RecipeNames 到gRecipeNames
            string strWorkingIR400RecipeNames = File.ReadAllText(ConfigPaths.WorkingIR400RecipeNames);
            var gRecipeNames = strWorkingIR400RecipeNames.JsonToEntity<RecipeNames>();

            try
            {
                /*ViewModelNew.ContainerProvider = Container;
                gAdsHelper = new AdsHelper();
                gAdsHelper.Connect();
                gAdsHelper.Write(RecipePLCCmdConstants.RecipeTypeIndexHMI, 0);
                Thread.Sleep(500);
                //获取配方名列表
                var arrTempRecipNmae = gAdsHelper.Read<string[]>(RecipePLCCmdConstants.RecipeNames);
                var keyId = 0;
                foreach (var recipeName in arrTempRecipNmae)
                {
                    if (!string.IsNullOrWhiteSpace(recipeName))
                    {
                        gRecipeNames.ChRecipeNameList.Add(new RecipeKeyValue(keyId++, recipeName));
                    }
                }
                //切换为Cooling配方
                gAdsHelper.Write(RecipePLCCmdConstants.RecipeTypeIndexHMI, 1);
                Thread.Sleep(500);
                arrTempRecipNmae = gAdsHelper.Read<string[]>(RecipePLCCmdConstants.RecipeNames);

                keyId = 0;
                foreach (var recipeName in arrTempRecipNmae)
                {
                    if (!string.IsNullOrWhiteSpace(recipeName))
                    {
                        gRecipeNames.CoolingRecipeNameList.Add(new RecipeKeyValue(keyId++, recipeName));
                    }
                }
                //切换为CH配方
                gAdsHelper.Write(RecipePLCCmdConstants.RecipeTypeIndexHMI, 0);*/
            }
            catch (Exception ex)
            {
                const string errorMessage = "ADS 连接失败，请检查网络配置和 PLC 配置。";
                AppLog.Error(errorMessage, ex);
                throw new InvalidOperationException($"{errorMessage}\n详细信息：{ex.Message}");
            }

            /*var curRecipeSeqInfoList = dBAccessService.GetRecipeSeqInfoList();
            if (curRecipeSeqInfoList == null)
            {
                const string errorMessage = "数据库连接不上";
                AppLog.Error(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
            gRecipeNames.DbRecipeSeqInfo = curRecipeSeqInfoList;*/
            //gRecipeNames.RecipeNameList = new ObservableCollection<RecipeKeyValue>();
            //int id = 0;
            //foreach (var recipeSeqInfo in curRecipeSeqInfoList)
            //{
            //    gRecipeNames.RecipeNameList.Add(new RecipeKeyValue(id++, recipeSeqInfo.RecipeName));
            //}

            //{
            //    new RecipeKeyValue(0, "配方ABC"),
            //    new RecipeKeyValue(1, "配方A"),
            //    new RecipeKeyValue(2, "配方B"),
            //    new RecipeKeyValue(3, "配方C"),
            //    new RecipeKeyValue(4, "配方AB"),
            //    new RecipeKeyValue(5, "配方AC"),
            //    new RecipeKeyValue(6, "配方BC")
            //};

            //注册共享单例服务
            /*containerRegistry.RegisterSingleton<AdsHelper>(() => gAdsHelper);*/
            containerRegistry.RegisterSingleton<Operator>();
            containerRegistry.RegisterSingleton<RecipeNames>(() => gRecipeNames);

            #endregion 注册全局共享 AdsHelper、RecipeNames、Operator   配方信息：CH配方、Cooling配方、Sequence
        }

        /// <summary>
        /// 初始化完成
        /// </summary>
        protected override void OnInitialized()
        {
            // 初始化窗口
            if (Current.MainWindow != null && Current.MainWindow.DataContext is Common.IConfigureService service)
                service.Configure();

            base.OnInitialized();

            // 启动时进行配置验证
            PerformStartupValidation();
        }

        /// <summary>
        /// 执行启动时配置验证
        /// </summary>
        private void PerformStartupValidation()
        {
            try
            {
                _logger.Info("开始执行启动时配置验证...");

                // 从IOC容器获取验证器实例
                var validator = App.GetInstance<SS200ConfigurationValidator>();
                //var validator = Container.Resolve<Services.SS200ConfigurationValidator>();

                // 执行验证
                var result = validator.ValidateOnStartup();

                _logger.Info($"配置验证完成，结果: {(result.IsValid ? "通过" : "有警告")}");
            }
            catch (Exception ex)
            {
                _logger.Error($"启动时配置验证失败: {ex.Message}", ex);

                // 显示错误消息，但不阻止程序启动
                MessageBox.Show(
                    $"配置验证过程中发生错误，但程序将继续运行。\n错误信息: {ex.Message}",
                    "配置验证警告",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// 将Configs目录下的所有配置文件和子目录复制到指定的工作路径
        /// </summary>
        /// <param name="sourceDir">源配置目录</param>
        /// <param name="targetDir">目标工作目录</param>
        /// <param name="overwrite">是否覆盖已存在的文件</param>
        private void CopyConfigFiles(string sourceDir, string targetDir, bool overwrite = false)
        {
            try
            {
                // 确保目标目录存在
                Directory.CreateDirectory(targetDir);
                _logger.Info($"确保目录存在: {targetDir}");

                // 复制所有文件
                foreach (var file in Directory.GetFiles(sourceDir))
                {
                    var fileName = Path.GetFileName(file);
                    var destFile = Path.Combine(targetDir, fileName);

                    if (overwrite || !File.Exists(destFile))
                    {
                        File.Copy(file, destFile, overwrite);
                        _logger.Info($"复制文件: {file} -> {destFile}");
                    }
                }

                // 递归复制子目录
                foreach (var directory in Directory.GetDirectories(sourceDir))
                {
                    var dirName = Path.GetFileName(directory);
                    var destDir = Path.Combine(targetDir, dirName);
                    CopyConfigFiles(directory, destDir, overwrite);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"复制配置文件时出错: {ex.Message}", ex);
                throw;
            }
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            //读取配置文件
            try
            {
                // 初始化应用程序配置
                InitializeConfiguration();

                _logger.Info("App启动中...");

                //单例运行
                if (CheckAndHandleSingleInstance())
                {
                    //正常启动程序
                    base.OnStartup(e);
                    //UI线程的异常捕捉
                    DispatcherUnhandledException += App_DispatcherUnhandledException;
                }
                else
                {
                    Shutdown(); // 检测到其他实例运行，关闭当前实例
                }
            }
            catch (Exception ex)
            {
                _logger.Error("应用程序启动失败，请联系开发人员！错误信息", ex);
                MessageBox.Show($"应用程序启动失败，请联系开发人员！\r\n错误信息：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                Shutdown(); // 关闭应用程序
            }
        }

        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                e.Handled = true;
                AppLog.Error($"未捕获错误_{MethodBase.GetCurrentMethod()?.Name}", e.Exception);
                if (e.Exception.InnerException == null)
                {
                    MessageBox.Show("（1）发生了一个错误！请联系开发人员！" + Environment.NewLine
                                       + "（2）错误源：" + e.Exception.Source + Environment.NewLine
                                       + "（3）详细信息：" + e.Exception.Message + Environment.NewLine
                                       + "（4）报错区域：" + e.Exception.StackTrace);
                }
                else
                {
                    MessageBox.Show("（1）发生了一个错误！请联系开发人员！" + Environment.NewLine
                                        + "（2）错误源：" + e.Exception.InnerException.Source + Environment.NewLine
                                        + "（3）错误信息：" + e.Exception.Message + Environment.NewLine
                                        + "（4）详细信息：" + e.Exception.InnerException.Message + Environment.NewLine
                                        + "（5）报错区域：" + e.Exception.InnerException.StackTrace);
                }
            }
            catch (Exception ex)
            {
                AppLog.Error($"程序发生致命错误，将终止，请联系运营商！_{MethodBase.GetCurrentMethod()?.Name}", ex);
                //此时程序出现严重异常，将强制结束退出
                MessageBox.Show($"程序发生致命错误，将终止，请联系运营商！错误信息：{ex.Message}");
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                _logger.Info("App正在退出...");
                base.OnExit(e);
                _logger.Info($"App已退出{Environment.NewLine}");
            }
            catch (Exception ex)
            {
                _logger.Error($"App退出时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Shows a custom error message.
        /// </summary>
        /// <param name="message">The error message to display.</param>
        private void ShowErrorMessage(string message)
        {
            MessageBox.Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// 初始化应用程序配置
        /// </summary>
        private void InitializeConfiguration()
        {    // 配置log4net
            ConfigureLogging();

            // 创建并确保日志目录存在
            Directory.CreateDirectory(ConfigPaths.LogsDir);
            AppLog.UpdateFolder(ConfigPaths.LogsDir);

            // 确保工作目录存在
            Directory.CreateDirectory(Golbal.WorkRootPath);
            _logger.Info($"确保工作根目录: {Golbal.WorkRootPath}");

            // 复制配置文件
            CopyConfigFiles(ConfigPaths.AppConfigDir, ConfigPaths.WorkingConfigDir);
            _logger.Info($"配置文件已复制到: {Golbal.WorkRootPath}");

            // 检查调试标记
            Golbal.IsDevDebug = File.Exists(Path.Combine(Golbal.WorkRootPath, "debug.tag"));

            var currentDirectory = Directory.GetCurrentDirectory();
            _logger.Info($"应用程序当前工作目录: {currentDirectory}");

            // 确保Config.ini存在并读取
            EnsureConfigIniExists();

            // 读取配置
            AppIniConfig = PubHelper.GetAppIniConfig(ConfigPaths.WorkingConfigIni);
        }

        /// <summary>
        /// 配置日志系统
        /// </summary>
        private void ConfigureLogging()
        {
            if (File.Exists(ConfigPaths.WorkingLog4netConfig))
            {
                XmlConfigurator.Configure(new FileInfo(ConfigPaths.WorkingLog4netConfig));
                _logger.Info($"使用工作目录下的log4net配置: {ConfigPaths.WorkingLog4netConfig}");
            }
            else
            {
                XmlConfigurator.Configure(new FileInfo(ConfigPaths.AppLog4netConfig));
                _logger.Info($"使用应用程序目录下的log4net配置: {ConfigPaths.AppLog4netConfig}");
            }
        }

        /// <summary>
        /// 确保Config.ini文件存在于工作目录
        /// </summary>
        private void EnsureConfigIniExists()
        {
            if (!File.Exists(ConfigPaths.WorkingConfigIni))
            {
                File.Copy(ConfigPaths.ConfigIniSource, ConfigPaths.WorkingConfigIni);
                _logger.Info($"复制Config.ini到工作目录: {ConfigPaths.WorkingConfigIni}");
            }
        }

        /// <summary>
        /// 检查并处理单例运行
        /// </summary>
        /// <returns>是否为首个实例或已成功处理其他实例</returns>
        private bool CheckAndHandleSingleInstance()
        {
            IMutex = new Mutex(true, "Zishan.SS200.Cmd_Run_App");
            if (IMutex.WaitOne(0, false))
            {
                return true; // 是首个实例
            }

            // 处理已存在的实例
            return HandleExistingInstances();
        }

        /// <summary>
        /// 处理已存在的实例
        /// </summary>
        /// <returns>是否成功处理</returns>
        private bool HandleExistingInstances()
        {
            try
            {
                var currentProcess = Process.GetCurrentProcess();
                var processes = Process.GetProcessesByName(currentProcess.ProcessName);
                var processesToKill = processes.Where(p => p.Id != currentProcess.Id).ToList();

                if (!processesToKill.Any())
                    return false;

                var result = MessageBox.Show(
                    "发现正在运行的相同程序，是否关闭之前的程序并继续运行？",
                    "提示",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                {
                    _logger.Info("用户选择不关闭现有程序，当前实例将退出");
                    return false;
                }

                foreach (var process in processesToKill)
                {
                    try
                    {
                        process.Kill();
                        _logger.Info($"已终止进程 - ID: {process.Id}, 名称: {process.ProcessName}");
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"终止进程失败 - ID: {process.Id}", ex);
                    }
                }

                return true; // 成功处理其他实例
            }
            catch (Exception ex)
            {
                _logger.Error("处理重复进程时发生错误", ex);
                ShowErrorMessage("处理程序实例时发生错误，请重试或联系技术支持。");
                return false;
            }
        }
    }
}