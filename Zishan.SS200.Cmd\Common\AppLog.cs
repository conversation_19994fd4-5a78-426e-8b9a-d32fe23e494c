﻿using log4net;
using log4net.Appender;
using log4net.Config;
using System;
using System.IO;
using System.Linq;

namespace Zishan.SS200.Cmd.Common
{
    /// <summary>
    /// 使用Log4net插件的log日志对象
    /// </summary>
    public static class AppLog
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(AppLog));

        static AppLog()
        {
            //var configLog4netFilePath =AppDomain.CurrentDomain.SetupInformation.ConfigurationFile;
            var configLog4netFilePath = AppDomain.CurrentDomain.BaseDirectory + "log4net.config";
            XmlConfigurator.ConfigureAndWatch(new FileInfo(configLog4netFilePath));
            //改变默认的日志位置
            //UpdateFolder(Constants._AppLogPath);
            //log = LogManager.GetLogger(typeof(AppLog));

            // 添加以下行
            //SetAdditionalLogPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AdditionalLogs"));
        }

        /// <summary>
        /// 改变默认的日志位置
        /// </summary>
        /// <param name="folder"></param>
        public static void UpdateFolder(string folder)
        {
            var storedPath = LogManager.GetRepository();
            var appenders = storedPath.GetAppenders();
            //var targetFolder = appenders.First(m => m.Name.Equals("InfoAppender")) as RollingFileAppender;//如果是要指定改某个appender,则在这里指定appender
            //targetFolder.File = folder;

            //一般多种日志目录是写在一起
            if (appenders == null) return;
            foreach (var app in appenders)
            {
                //if (app.Name.Equals("ErrorAppender") || app.Name.Equals("InfoAppender"))
                //{
                var ra = app as RollingFileAppender;
                if (ra != null)
                {
                    ra.File = folder + "\\" + new FileInfo(ra.File).Name;
                    ra.ActivateOptions();
                }

                //}
            }
        }

        /// <summary>
        /// 设置附加日志路径，将日志文件保存到两个不同的路径。
        /// </summary>
        /// <param name="additionalPath">附加日志文件的路径。</param>
        public static void SetAdditionalLogPath(string additionalPath)
        {
            var repository = LogManager.GetRepository();
            var appenders = repository.GetAppenders();
            string originalFile = string.Empty;

            foreach (var appender in appenders)
            {
                if (appender is RollingFileAppender fileAppender)
                {
                    originalFile = fileAppender.File;
                    var fileName = Path.GetFileName(originalFile);
                    var newFile = Path.Combine(additionalPath, fileName);

                    // 创建一个新的RollingFileAppender，复制原有的设置
                    var newAppender = new RollingFileAppender();
                    newAppender.Name = fileAppender.Name + "_Additional";
                    newAppender.File = newFile;
                    newAppender.AppendToFile = fileAppender.AppendToFile;
                    newAppender.RollingStyle = fileAppender.RollingStyle;
                    newAppender.MaxSizeRollBackups = fileAppender.MaxSizeRollBackups;
                    newAppender.MaximumFileSize = fileAppender.MaximumFileSize;
                    newAppender.StaticLogFileName = fileAppender.StaticLogFileName;
                    newAppender.DatePattern = fileAppender.DatePattern;
                    newAppender.Layout = fileAppender.Layout;
                    newAppender.LockingModel = new MinimalLockDeleteEmpty();

                    newAppender.ActivateOptions();

                    // 将新的Appender添加到仓库中
                    ((log4net.Repository.Hierarchy.Hierarchy)repository).Root.AddAppender(newAppender);
                }
            }

            if (!string.IsNullOrWhiteSpace(originalFile))
            {
                // 复制历史日志文件到附加路径
                var logFiles = Directory.GetFiles(Path.GetDirectoryName(originalFile) ?? string.Empty, "*.log");
                foreach (var logFile in logFiles)
                {
                    var destFile = Path.Combine(additionalPath, Path.GetFileName(logFile));
                    if (!File.Exists(destFile))
                    {
                        File.Copy(logFile, destFile);
                    }
                }

                // 删除附加路径中的多余日志文件
                var additionalLogFiles = Directory.GetFiles(additionalPath, "*.log");
                foreach (var additionalLogFile in additionalLogFiles)
                {
                    var fileName = Path.GetFileName(additionalLogFile);
                    if (logFiles.All(logFile => Path.GetFileName(logFile) != fileName))
                    {
                        File.Delete(additionalLogFile);
                    }
                }
            }

            // 重新配置log4net
            repository.Configured = true;
        }

        public static void Debug(object message)
        {
            log.Debug(message);
        }

        public static void DebugFormatted(string format, params object[] args)
        {
            log.DebugFormat(format, args);
        }

        public static void Info(string message, params object[] args)
        {
            if (args?.Length > 0)
            {
                log.InfoFormat(message, args);
            }
            else
            {
                log.Info(message);
            }
        }

        public static void InfoFormatted(string format, params object[] args)
        {
            log.InfoFormat(format, args);
        }

        public static void Warn(object message)
        {
            log.Warn(message);
        }

        public static void Warn(object message, Exception exception)
        {
            log.Warn(message, exception);
        }

        public static void WarnFormatted(string format, params object[] args)
        {
            log.WarnFormat(format, args);
        }

        public static void Error(string message, Exception ex = null)
        {
            if (ex != null)
            {
                log.Error($"{message} | 异常详情: {ex}");
            }
            else
            {
                log.Error(message);
            }
        }

        public static void Error(object message, Exception exception)
        {
            log.Error(message, exception);
        }

        public static void ErrorFormatted(string format, params object[] args)
        {
            log.ErrorFormat(format, args);
        }

        public static void Fatal(object message)
        {
            log.Fatal(message);
        }

        public static void Fatal(object message, Exception exception)
        {
            log.Fatal(message, exception);
        }

        public static void FatalFormatted(string format, params object[] args)
        {
            log.FatalFormat(format, args);
        }
    }
}

//自定义log4net扩展
namespace log4net.Appender
{
    /// <summary>
    /// 解决Log4Net生成空日志文件的方法，并确保AdditionalLogs与之前的Logs保持一致
    /// 最后在log4net.config配置文件中将类插入完成调用 <lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
    /// </summary>
    public class MinimalLockDeleteEmpty : FileAppender.MinimalLock
    {
        public override void ReleaseLock()
        {
            base.ReleaseLock();

            var logFile = new FileInfo(CurrentAppender.File);
            if (logFile.Exists)
            {
                // 判断文件是否被占用
                if (IsFileLocked(logFile))
                {
                    return;
                }

                var arrText = File.ReadAllText(CurrentAppender.File);
                if (string.IsNullOrWhiteSpace(arrText))
                {
                    logFile.Delete();
                }
                //else
                //{
                //    // 复制日志文件到AdditionalLogs文件夹
                //    var additionalLogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AdditionalLogs", logFile.Name);
                //    File.Copy(CurrentAppender.File, additionalLogPath, true);
                //}
            }
        }

        private bool IsFileLocked(FileInfo file)
        {
            FileStream stream = null;

            try
            {
                stream = file.Open(FileMode.Open, FileAccess.Read, FileShare.None);
            }
            catch (IOException)
            {
                // 文件被锁定
                return true;
            }
            finally
            {
                stream?.Close();
            }

            // 文件未被锁定
            return false;
        }
    }
}